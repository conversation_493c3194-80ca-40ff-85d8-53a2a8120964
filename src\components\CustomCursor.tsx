"use client"

import { useState, useEffect } from "react"

const CustomCursor = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovering, setIsHovering] = useState(false)
  const [isClicking, setIsClicking] = useState(false)

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    const handleMouseEnter = () => setIsHovering(true)
    const handleMouseLeave = () => setIsHovering(false)
    const handleMouseDown = () => setIsClicking(true)
    const handleMouseUp = () => setIsClicking(false)

    // Add mouse move listener
    document.addEventListener('mousemove', updateMousePosition)
    
    // Add hover listeners to interactive elements
    const interactiveElements = document.querySelectorAll('a, button, input, textarea, [role="button"]')
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter)
      el.addEventListener('mouseleave', handleMouseLeave)
    })

    // Add click listeners
    document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mousemove', updateMousePosition)
      document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mouseup', handleMouseUp)
      
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter)
        el.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [])

  return (
    <>
      {/* Main cursor dot */}
      <div
        className="fixed pointer-events-none z-[9999] mix-blend-difference"
        style={{
          left: mousePosition.x - 4,
          top: mousePosition.y - 4,
          transform: `scale(${isClicking ? 0.8 : 1})`,
          transition: 'transform 0.1s ease-out'
        }}
      >
        <div
          className={`w-2 h-2 rounded-full transition-all duration-200 ${
            isHovering 
              ? 'bg-white shadow-[0_0_20px_rgba(255,255,255,0.8)]' 
              : 'bg-[rgb(233,100,55)] shadow-[0_0_15px_rgba(233,100,55,0.6)]'
          }`}
          style={{
            animation: 'pulse 2s infinite'
          }}
        />
      </div>

      {/* Outer ring */}
      <div
        className="fixed pointer-events-none z-[9998]"
        style={{
          left: mousePosition.x - 15,
          top: mousePosition.y - 15,
          transform: `scale(${isHovering ? 1.5 : 1}) ${isClicking ? 'scale(0.9)' : ''}`,
          transition: 'transform 0.2s ease-out'
        }}
      >
        <div
          className={`w-8 h-8 rounded-full border transition-all duration-300 ${
            isHovering 
              ? 'border-white border-opacity-60' 
              : 'border-[rgb(233,100,55)] border-opacity-40'
          }`}
          style={{
            animation: 'rotate 4s linear infinite'
          }}
        />
      </div>

      {/* Global cursor styles */}
      <style jsx global>{`
        * {
          cursor: none !important;
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.1);
          }
        }
        
        @keyframes rotate {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        
        /* Hide cursor on touch devices */
        @media (hover: none) and (pointer: coarse) {
          * {
            cursor: auto !important;
          }
        }
      `}</style>
    </>
  )
}

export default CustomCursor
