<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="#101314"/>
  
  <!-- Orange accent background -->
  <rect x="0" y="0" width="600" height="630" fill="#e96437"/>
  
  <!-- Code bracket icon -->
  <g transform="translate(150, 200)">
    <path d="M50 75L75 50L100 75M75 50V125M125 75L150 50L175 75M150 50V125M50 125L75 150L100 125M125 125L150 150L175 125" stroke="white" stroke-width="8" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Main text -->
  <text x="650" y="200" font-family="Arial, sans-serif" font-size="64" font-weight="bold" fill="white">
    <PERSON><PERSON> Raheem
  </text>
  
  <!-- Subtitle -->
  <text x="650" y="280" font-family="Arial, sans-serif" font-size="36" fill="#e96437">
    Frontend Developer
  </text>
  
  <!-- Skills -->
  <text x="650" y="340" font-family="Arial, sans-serif" font-size="24" fill="#cccccc">
    React.js • Next.js • UI/UX Design
  </text>
  
  <!-- Description -->
  <text x="650" y="400" font-family="Arial, sans-serif" font-size="20" fill="#999999">
    Building modern, responsive web applications
  </text>
  <text x="650" y="430" font-family="Arial, sans-serif" font-size="20" fill="#999999">
    with cutting-edge technologies
  </text>
  
  <!-- Website -->
  <text x="650" y="500" font-family="Arial, sans-serif" font-size="18" fill="#e96437">
    moazraheem.dev
  </text>
</svg>
