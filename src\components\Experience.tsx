"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Calendar,
  MapPin,
  Briefcase,
  Building,
} from "lucide-react"
import { useState } from "react"

const ProfessionalExperience = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  const experiences = [
    {
      title: "Instructor",
      company: "Innvoytech",
      period: "09/2024 – 11/2025",
      location: "Remote",
      type: "Full-time",
      description:
        "Leading comprehensive web development courses and mentoring students in modern JavaScript frameworks. Developing curriculum for MERN Stack development and providing hands-on guidance to aspiring developers.",
      keySkills: ["MERN Stack", "React.js", "Next.js", "Teaching", "Curriculum Development", "Student Mentoring"],
      achievements: [
        "Mentored 50+ students in web development",
        "Developed comprehensive MERN Stack curriculum",
        "Achieved 95% student satisfaction rate",
      ],
    },
    {
      title: "UI Developer",
      company: "Crective",
      period: "02/2025 – 03/2025",
      location: "Remote",
      type: "Contract",
      description:
        "Developed responsive user interfaces and integrated RESTful APIs for enhanced user experiences. Collaborated with design teams to implement pixel-perfect interfaces and optimize application performance.",
      keySkills: [
        "React.js",
        "Next.js",
        "UI/UX Design",
        "API Integration",
        "Responsive Design",
        "Performance Optimization",
      ],
      achievements: [
        "Improved application performance by 40%",
        "Successfully integrated 15+ API endpoints",
        "Delivered projects ahead of schedule",
      ],
    },
    {
      title: "UI Designer/Frontend React JS Developer",
      company: "Genius Mind Zone",
      period: "12/2024 – 01/2025",
      location: "Remote",
      type: "Freelance",
      description:
        "Created intuitive user interfaces with focus on responsive design and user experience optimization. Worked closely with clients to translate business requirements into functional web applications.",
      keySkills: ["React.js", "UI Design", "Responsive Design", "User Experience", "Client Communication", "Figma"],
      achievements: [
        "Designed and developed 5+ client projects",
        "Achieved 100% client satisfaction",
        "Reduced development time by 30% through reusable components",
      ],
    },
    {
      title: "Frontend Developer",
      company: "Pak Freelancer Software House",
      period: "07/2023 – 01/2024",
      location: "On-site",
      type: "Full-time",
      description:
        "Built responsive web applications and collaborated with design teams to implement pixel-perfect interfaces. Focused on creating maintainable code and following best practices in frontend development.",
      keySkills: ["HTML5", "CSS3", "JavaScript", "Responsive Design", "Team Collaboration", "Version Control"],
      achievements: [
        "Developed 10+ responsive web applications",
        "Collaborated with cross-functional teams",
        "Maintained 99% code quality standards",
      ],
    },
  ]

  return (
    <div id="experience" className="min-h-screen text-white overflow-hidden py-12 md:py-16 lg:py-20"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-48 h-48 sm:w-72 sm:h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-64 h-64 sm:w-96 sm:h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-48 h-48 sm:w-64 sm:h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 lg:px-16 xl:px-20 pb-8 md:pb-12">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl py-6 md:py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Experience
            </h1>
            <div
              className="hidden md:block w-16 lg:w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Experience Grid */}
      <div className="relative z-10 px-6 md:px-8 lg:px-16 xl:px-20 pb-8 md:pb-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10 max-w-7xl mx-auto">
          {experiences.map((experience, index) => {
            const isHovered = hoveredCard === index

            return (
              <div
                key={index}
                className="group"
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <Card
                  className={`
                    relative h-[500px] transition-all duration-500 cursor-pointer overflow-hidden
                    backdrop-blur-sm border-2
                    ${isHovered ? "scale-105" : ""}
                  `}
                  style={{
                    backgroundColor: "rgba(16, 19, 20, 0.8)",
                    borderColor: isHovered ? "rgb(233, 100, 55)" : "rgba(233, 100, 55, 0.3)",
                    boxShadow: isHovered
                      ? "0 0 30px rgba(233, 100, 55, 0.3), inset 0 0 30px rgba(233, 100, 55, 0.1)"
                      : "0 0 20px rgba(0, 0, 0, 0.3)",
                  }}
                >
                  <CardContent className="px-6 pt-4 pb-4 flex-1 flex flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="p-2 rounded-lg"
                          style={{ backgroundColor: "rgba(233, 100, 55, 0.1)" }}
                        >
                          <Briefcase className="w-5 h-5" style={{ color: "rgb(233, 100, 55)" }} />
                        </div>
                        <div>
                          <Badge
                            variant="outline"
                            className="text-xs"
                            style={{
                              borderColor: "rgb(233, 100, 55)",
                              color: "rgb(233, 100, 55)",
                              backgroundColor: "rgba(233, 100, 55, 0.1)",
                            }}
                          >
                            {experience.type}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Title and Company */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-[rgb(233,100,55)] transition-colors">
                        {experience.title}
                      </h3>
                      <div className="flex items-center text-[rgb(233,100,55)] font-semibold mb-2">
                        <Building className="w-4 h-4 mr-2" />
                        {experience.company}
                      </div>
                    </div>

                    {/* Meta Information */}
                    <div className="flex flex-wrap gap-4 mb-4 text-sm">
                      <div className="flex items-center text-gray-300">
                        <Calendar className="w-4 h-4 mr-2 text-[rgb(233,100,55)]" />
                        {experience.period}
                      </div>
                      <div className="flex items-center text-gray-300">
                        <MapPin className="w-4 h-4 mr-2 text-[rgb(233,100,55)]" />
                        {experience.location}
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-400 mb-4 leading-relaxed text-sm flex-1">
                      {experience.description}
                    </p>

                    {/* Skills */}
                    <div className="mb-4">
                      <h4 className="text-white font-semibold mb-2 text-sm">Key Skills:</h4>
                      <div className="flex flex-wrap gap-1">
                        {experience.keySkills.slice(0, 4).map((skill, skillIndex) => (
                          <span
                            key={skillIndex}
                            className="px-2 py-1 text-xs rounded-full"
                            style={{
                              backgroundColor: "rgba(233, 100, 55, 0.1)",
                              color: "rgb(233, 100, 55)",
                              border: "1px solid rgba(233, 100, 55, 0.3)",
                            }}
                          >
                            {skill}
                          </span>
                        ))}
                        {experience.keySkills.length > 4 && (
                          <span
                            className="px-2 py-1 text-xs rounded-full"
                            style={{
                              backgroundColor: "rgba(233, 100, 55, 0.1)",
                              color: "rgb(233, 100, 55)",
                              border: "1px solid rgba(233, 100, 55, 0.3)",
                            }}
                          >
                            +{experience.keySkills.length - 4}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Achievements */}
                    <div>
                      <h4 className="text-white font-semibold mb-2 text-sm">Key Achievements:</h4>
                      <ul className="space-y-1">
                        {experience.achievements.slice(0, 2).map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start text-gray-300 text-xs">
                            <div className="w-1 h-1 bg-[rgb(233,100,55)] rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default ProfessionalExperience
