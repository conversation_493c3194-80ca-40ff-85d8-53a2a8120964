"use client"

import { useState, useEffect } from "react"
import { HiArrowUp } from "react-icons/hi2"

const BackToTop = () => {
  const [isVisible, setIsVisible] = useState(false)

  // Show button when page is scrolled up to given distance
  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true)
    } else {
      setIsVisible(false)
    }
  }

  // Set the scroll event listener
  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility)
    return () => {
      window.removeEventListener('scroll', toggleVisibility)
    }
  }, [])

  // Scroll to top smoothly
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <>
      {isVisible && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-50 group p-3 bg-[rgb(233,100,55)] text-white rounded-full shadow-lg hover:bg-[rgb(233,100,55)]/90 hover:shadow-xl transition-all duration-300 hover:scale-110 active:scale-95"
          aria-label="Back to top"
          style={{
            animation: 'fadeInUp 0.3s ease-in-out'
          }}
        >
          <HiArrowUp className="w-5 h-5 transition-transform duration-300 group-hover:translate-y-[-2px]" />
        </button>
      )}
      
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </>
  )
}

export default BackToTop
