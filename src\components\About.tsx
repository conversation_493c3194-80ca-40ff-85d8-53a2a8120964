'use client';

import React from 'react';

const About = () => {
  return (
   <section id="about" className="min-h-screen flex flex-col lg:flex-row items-center py-12 md:py-16 lg:py-20" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
  {/* Left Side - Text Content */}
  <div className="w-full lg:w-1/2 flex flex-col justify-center p-6 md:p-8 lg:p-16 xl:p-20 text-white">
    {/* Main Title */}
    <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 md:mb-8 lg:mb-10 text-white">
      LET ME INTRODUCE MYSELF
    </h2>

    {/* Description Text */}
    <div className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-gray-300 leading-relaxed space-y-4 md:space-y-6">
      <p>
        I’m a passionate <span className="text-[rgb(233, 100, 55)] font-semibold">Frontend Developer</span> and <span className="text-[rgb(233, 100, 55)] font-semibold">UI Designer</span> with strong experience in building modern, responsive web applications. I specialize in technologies like{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">React.js</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Next.js</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">JavaScript (ES6+)</span>, along with advanced styling using{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Tailwind CSS</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Bootstrap</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Sass</span>.
      </p>

      <p>
        I’ve built and contributed to multiple real-world projects including CRM platforms, dashboards, marketing tools, and downloader UIs. I use tools like{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Vite</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Framer Motion</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Shadcn UI</span>, and maintain Git-based workflows.
      </p>

      <p>
        I'm also familiar with backend fundamentals using{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Node.js</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Express.js</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">MongoDB</span> as part of the MERN stack. I'm currently seeking opportunities to apply my frontend expertise and continue growing in real-world development environments.
      </p>
    </div>
  </div>

  {/* Right Side - Profile Image */}
  <div className="w-full lg:w-1/2 flex justify-center items-center p-6 md:p-8 lg:p-16 xl:p-20 mt-8 lg:mt-0">
    <div className="relative">
      {/* Decorative background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgb(233,100,55)]/20 to-transparent rounded-3xl transform rotate-6"></div>
      <div className="absolute inset-0 bg-gradient-to-tl from-[rgb(233,100,55)]/10 to-transparent rounded-3xl transform -rotate-3"></div>

      {/* Main profile image container */}
      <div className="relative w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[400px] lg:h-[500px] xl:w-[450px] xl:h-[550px]">
        <div className="w-full h-full rounded-3xl overflow-hidden shadow-2xl border-4 border-[rgb(233,100,55)]/30 bg-gradient-to-br from-gray-800 to-gray-900">
          <img
            src="/moaz%20raheem.png"
            alt="Moaz Raheem - Frontend Developer Portrait"
            className="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-500"
            loading="lazy"
            onError={(e) => {
              // Fallback to placeholder if image fails to load
              const target = e.target as HTMLImageElement;
              target.src = "/experience-image-WpHjoI0V.png";
              target.alt = "Developer working on laptop";
            }}
          />
        </div>

        {/* Floating accent elements */}
        <div className="absolute -top-4 -right-4 w-8 h-8 bg-[rgb(233,100,55)] rounded-full animate-bounce"></div>
        <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-[rgb(233,100,55)]/30 rounded-full animate-pulse"></div>
        <div className="absolute top-1/4 -left-8 w-6 h-6 bg-white/20 rounded-full animate-ping"></div>
      </div>
    </div>
  </div>
</section>

  );
};

export default About;
