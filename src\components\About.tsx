'use client';

import React from 'react';

const About = () => {
  return (
   <section className="min-h-screen flex flex-col lg:flex-row items-center py-12 md:py-16 lg:py-20" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
  {/* Left Side - Text Content */}
  <div className="w-full lg:w-1/2 flex flex-col justify-center p-6 md:p-8 lg:p-16 xl:p-20 text-white">
    {/* Main Title */}
    <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 md:mb-8 lg:mb-10 text-white">
      LET ME INTRODUCE MYSELF
    </h2>

    {/* Description Text */}
    <div className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-gray-300 leading-relaxed space-y-4 md:space-y-6">
      <p>
        I’m a passionate <span className="text-[rgb(233, 100, 55)] font-semibold">Frontend Developer</span> and <span className="text-[rgb(233, 100, 55)] font-semibold">UI Designer</span> with strong experience in building modern, responsive web applications. I specialize in technologies like{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">React.js</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Next.js</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">JavaScript (ES6+)</span>, along with advanced styling using{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Tailwind CSS</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Bootstrap</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Sass</span>.
      </p>

      <p>
        I’ve built and contributed to multiple real-world projects including CRM platforms, dashboards, marketing tools, and downloader UIs. I use tools like{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Vite</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Framer Motion</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Shadcn UI</span>, and maintain Git-based workflows.
      </p>

      <p>
        I'm also familiar with backend fundamentals using{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Node.js</span>,{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">Express.js</span>, and{' '}
        <span className="text-[rgb(233, 100, 55)] font-semibold">MongoDB</span> as part of the MERN stack. I'm currently seeking opportunities to apply my frontend expertise and continue growing in real-world development environments.
      </p>
    </div>
  </div>

  {/* Right Side - Image */}
  <div className="w-full lg:w-1/2 flex justify-center items-center p-6 md:p-8 lg:p-16 xl:p-20 mt-8 lg:mt-0">
    <div className="relative">
      <div className="w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80 lg:w-96 lg:h-[500px] xl:h-[600px] relative">
        <img
          src="/experience-image-WpHjoI0V.png"
          alt="Developer working on laptop"
          className="w-full h-full object-contain"
        />
      </div>
    </div>
  </div>
</section>

  );
};

export default About;
