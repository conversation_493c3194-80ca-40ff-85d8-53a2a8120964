"use client"

import { FaFacebook, FaGithub, FaInstagram, FaLinkedin, FaWhatsapp } from "react-icons/fa"
import { HiArrowUp } from "react-icons/hi2"

const Footer = () => {
  const socialLinks = [
    {
      icon: FaFacebook,
      href: "https://facebook.com",
      label: "Facebook"
    },
    {
      icon: FaGithub,
      href: "https://github.com",
      label: "GitHub"
    },
    {
      icon: FaInstagram,
      href: "https://instagram.com",
      label: "Instagram"
    },
    {
      icon: FaLinkedin,
      href: "https://linkedin.com",
      label: "LinkedIn"
    },
    {
      icon: FaWhatsapp,
      href: "https://whatsapp.com",
      label: "WhatsApp"
    }
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <footer className="relative text-white py-12 md:py-16 lg:py-20"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-10 left-10 w-32 h-32 sm:w-48 sm:h-48 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-10 right-10 w-48 h-48 sm:w-64 sm:h-64 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 md:px-8 lg:px-16 xl:px-20">
        {/* Main Footer Content */}
        <div className="text-center space-y-6 md:space-y-8 lg:space-y-10">
          {/* Find Me On Section */}
          <div className="space-y-3 md:space-y-4">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white">
              FIND ME ON
            </h2>
            <p className="text-gray-300 text-base sm:text-lg md:text-xl">
              Feel free to connect with me
            </p>
          </div>

          {/* Social Icons */}
          <div className="flex justify-center items-center gap-4 sm:gap-6 md:gap-8">
            {socialLinks.map((social, index) => {
              const IconComponent = social.icon
              return (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className="group"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full border-2 border-white flex items-center justify-center transition-all duration-300 hover:scale-110 hover:border-[rgb(233,100,55)] hover:bg-[rgb(233,100,55)]">
                    <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-white transition-colors duration-300" />
                  </div>
                </a>
              )
            })}
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-8 md:mt-12 lg:mt-16 pt-6 md:pt-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-6">
            <p className="text-gray-300 text-xs sm:text-sm md:text-base text-center md:text-left">
              Copyright © 2024. All rights reserved. Designed & developed by Moaz Developer.
            </p>

            {/* Back to Top Button */}
            <button
              onClick={scrollToTop}
              className="group flex items-center gap-2 px-3 sm:px-4 md:px-6 py-2 md:py-3 bg-transparent border-2 border-white text-white rounded-full hover:bg-[rgb(233,100,55)] hover:border-[rgb(233,100,55)] transition-all duration-300"
              aria-label="Back to top"
            >
              <HiArrowUp className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 transition-transform duration-300 group-hover:translate-y-[-2px]" />
              <span className="text-xs sm:text-sm md:text-base font-medium">Back to Top</span>
            </button>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
