"use client"

import { FaFacebook, FaGithub, Fa<PERSON><PERSON><PERSON>ram, FaLinkedin, FaWhatsapp } from "react-icons/fa"

const Footer = () => {
  const socialLinks = [
    {
      icon: FaFacebook,
      href: "https://www.facebook.com/moaz.raheem.3",
      label: "Facebook"
    },
    {
      icon: FaGithub,
      href: "https://github.com/mianmoaz786",
      label: "GitHub"
    },
    {
      icon: FaInstagram,
      href: "https://www.instagram.com/mianmoaz225/",
      label: "Instagram"
    },
    {
      icon: FaLinkedin,
      href: "https://www.linkedin.com/in/moaz-raheem-react-js-next-js-developer-50a05528a",
      label: "LinkedIn"
    },
    {
      icon: FaWhatsapp,
      href: "https://wa.me/923019739945",
      label: "WhatsApp"
    }
  ]



  return (
    <footer className="relative text-white py-12 md:py-16 lg:py-20"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-10 left-10 w-32 h-32 sm:w-48 sm:h-48 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-10 right-10 w-48 h-48 sm:w-64 sm:h-64 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 md:px-8 lg:px-16 xl:px-20">
        {/* Main Footer Content */}
        <div className="text-center space-y-6 md:space-y-8 lg:space-y-10">
          {/* Find Me On Section */}
          <div className="space-y-3 md:space-y-4">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white">
              FIND ME ON
            </h2>
            <p className="text-gray-300 text-base sm:text-lg md:text-xl">
              Feel free to connect with me
            </p>
          </div>

          {/* Social Icons */}
          <div className="flex justify-center items-center gap-4 sm:gap-6 md:gap-8">
            {socialLinks.map((social, index) => {
              const IconComponent = social.icon
              return (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className="group"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full border-2 border-white flex items-center justify-center transition-all duration-300 hover:scale-110 hover:border-[rgb(233,100,55)] hover:bg-[rgb(233,100,55)]">
                    <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-white transition-colors duration-300" />
                  </div>
                </a>
              )
            })}
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-8 md:mt-12 lg:mt-16 pt-6 md:pt-8 border-t border-gray-700">
          <div className="flex justify-center items-center">
            <p className="text-gray-300 text-xs sm:text-sm md:text-base text-center">
              Copyright © 2024. All rights reserved. Designed & developed by Moaz Developer.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
