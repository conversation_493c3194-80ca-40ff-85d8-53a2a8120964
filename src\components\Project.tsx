"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ExternalLink,
  FileText,
  Laptop,
  Smartphone,
  Globe,
  Code,
  Database,
  Eye,
  Calendar,
  Play,
  ImageIcon,
} from "lucide-react"
import { FaGithub } from "react-icons/fa"
import { useState } from "react"
import Image from "next/image"

const projects = [
  {
    id: 1,
    title: "TextUtils",
    description:
      "A web-based text manipulation tool that allows users to perform operations such as converting text to uppercase, lowercase, clearing text, and analyzing text statistics like word count and character count.",
    icon: FileText,
    image: "/photo-1551288049-bebda4e38f71.jpeg",
    link: "https://textutils-text-analyzer-app.netlify.app",
    github: "https://github.com/mianmoaz786/textutils-pro",
    tools: ["React.js", "Bootstrap", "JavaScript"],
    category: "Web App",
    status: "Live",
    year: "2024",
    color: "from-blue-500 to-purple-600",
    features: ["Text Analysis", "Case Conversion", "Word Count", "Character Count"],
  },
  {
    id: 2,
    title: "Plixi Template",
    description:
      "A responsive web template designed to adapt to various screen sizes and devices. It features a clean and modern design, built using HTML, CSS, and JavaScript with smooth animations and interactive elements.",
    icon: Laptop,
    image: "/photo-1467232004584-a241de8bcf5d.jpeg",
    link: "https://plixi-template.netlify.app",
    github: "https://github.com/mianmoaz786/Plixi",
    tools: ["React.js", "Sass", "Bootstrap", "JavaScript"],
    category: "Template",
    status: "Live",
    year: "2024",
    color: "from-green-500 to-teal-600",
    features: ["Responsive Design", "Modern UI", "Smooth Animations", "Cross-Browser"],
  },
  {
    id: 3,
    title: "Inko-Moko Dashboard",
    description:
      "A modern, responsive dashboard built with React.js. It features a sleek, intuitive layout, customizable components, and interactive elements like dynamic charts. Designed with a mobile-first approach.",
    icon: Code,
    image: "/photo-1554224155-6726b3ff858f.jpeg",
    link: "https://inko-moko-ui-template.netlify.app",
    github: "https://github.com/mianmoaz786/inko-moko",
    tools: ["React.js", "Sass", "Bootstrap", "Chart.js"],
    category: "Dashboard",
    status: "Live",
    year: "2024",
    color: "from-purple-500 to-pink-600",
    features: ["Dynamic Charts", "Mobile-First", "Customizable", "Interactive UI"],
  },
  {
    id: 4,
    title: "Snow Dream Studio",
    description:
      "A high-performance, responsive portfolio website using Next.js. Utilized shadcn/ui and Tailwind CSS for clean, component-based UI design with smooth animations and scroll-triggered effects.",
    icon: Globe,
    image: "/photo-1555066931-4365d14bab8c.jpeg",
    link: "https://snow-dream-studio-home-page.netlify.app",
    github: "https://github.com/mianmoaz786/snow-dream-studio",
    tools: ["Next.js", "Tailwind CSS", "Framer Motion", "GSAP"],
    category: "Portfolio",
    status: "Live",
    year: "2024",
    color: "from-cyan-500 to-blue-600",
    features: ["GSAP Animations", "SEO Optimized", "Component-Based", "High Performance"],
  },
  {
    id: 5,
    title: "CRM Platform",
    description:
      "A comprehensive Customer Relationship Management platform built with modern React architecture. Features include client management, task tracking, and analytics dashboard for business operations.",
    icon: Database,
    image: "/photo-1460925895917-afdab827c52f.jpeg",
    link: "https://crm-crective-demo.netlify.app",
    github: "https://github.com/mianmoaz786/Portfolio-master",
    tools: ["React.js", "Next.js", "API Integration", "MongoDB"],
    category: "Enterprise",
    status: "Demo",
    year: "2024",
    color: "from-orange-500 to-red-600",
    features: ["Client Management", "Task Tracking", "Analytics", "API Integration"],
  },
  {
    id: 6,
    title: "Social Downloader",
    description:
      "Contributed to the Social Downloader project by designing the YouTube section, which includes profile views, a video short downloader, and ensuring overall website responsiveness.",
    icon: Smartphone,
    image: "/photo-1611224923853-80b023f02d71.jpeg",
    link: "https://dashbone.social",
    github: "https://github.com/mianmoaz786/newsapp",
    tools: ["React.js", "Responsive Design", "API Integration"],
    category: "Social Media",
    status: "Live",
    year: "2024",
    color: "from-pink-500 to-rose-600",
    features: ["Video Download", "Profile Views", "Responsive", "Social Integration"],
  },
]

export default function Projects() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)
  const [imageLoaded, setImageLoaded] = useState<{ [key: number]: boolean }>({})
  const [imageError, setImageError] = useState<{ [key: number]: boolean }>({})

  const handleImageLoad = (projectId: number) => {
    setImageLoaded((prev) => ({ ...prev, [projectId]: true }))
  }

  const handleImageError = (projectId: number) => {
    setImageError((prev) => ({ ...prev, [projectId]: true }))
  }

  return (
    <div id="projects" className="min-h-screen text-white overflow-hidden py-12 md:py-16 lg:py-20"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-48 h-48 sm:w-72 sm:h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-64 h-64 sm:w-96 sm:h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-48 h-48 sm:w-64 sm:h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 lg:px-16 xl:px-20 pb-8 md:pb-12">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl py-6 md:py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Projects
            </h1>
            <div
              className="hidden md:block w-16 lg:w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="relative z-10 px-6 md:px-8 lg:px-16 xl:px-20 pb-8 md:pb-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10 max-w-7xl mx-auto">
          {projects.map((project, index) => {
            const IconComponent = project.icon
            const isHovered = hoveredCard === project.id
            const isImageLoaded = imageLoaded[project.id]
            const hasImageError = imageError[project.id]

            return (
              <div
                key={project.id}
                className="group"
                onMouseEnter={() => setHoveredCard(project.id)}
                onMouseLeave={() => setHoveredCard(null)}
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <Card
                  className={`
                    relative h-[700px] md:h-[600px] transition-all duration-500 cursor-pointer overflow-hidden
                    backdrop-blur-sm border-2
                    ${isHovered ? "scale-105" : ""}
                  `}
                  style={{
                    backgroundColor: "rgba(16, 19, 20, 0.8)",
                    borderColor: isHovered ? "rgb(233, 100, 55)" : "rgba(233, 100, 55, 0.3)",
                    boxShadow: isHovered
                      ? "0 0 30px rgba(233, 100, 55, 0.3), inset 0 0 30px rgba(233, 100, 55, 0.1)"
                      : "0 0 15px rgba(233, 100, 55, 0.1)",
                  }}
                >
                  {/* Project Image - Outside CardContent to avoid padding */}
                  <div className="relative h-56 overflow-hidden group w-full">
                      {/* Loading Skeleton */}
                      {!isImageLoaded && !hasImageError && (
                        <div
                          className="absolute inset-0 animate-pulse flex items-center justify-center"
                          style={{ backgroundColor: "rgba(233, 100, 55, 0.1)" }}
                        >
                          <ImageIcon className="w-12 h-12" style={{ color: "rgb(233, 100, 55)" }} />
                        </div>
                      )}

                      {!hasImageError ? (
                        <Image
                          src={project.image || "/placeholder.svg"}
                          alt={`${project.title} - Project Screenshot`}
                          fill
                          className={`
                            object-cover transition-all duration-700
                            ${isHovered ? "scale-110" : "scale-100"}
                            ${isImageLoaded ? "opacity-100" : "opacity-0"}
                          `}
                          onLoad={() => handleImageLoad(project.id)}
                          onError={() => handleImageError(project.id)}
                          crossOrigin="anonymous"
                        />
                      ) : (
                        // Fallback design with project branding
                        <div
                          className={`
                          w-full h-full flex items-center justify-center relative
                          ${isHovered ? "scale-110" : "scale-100"}
                          transition-all duration-700
                        `}
                          style={{ backgroundColor: "rgb(233, 100, 55)" }}
                        >
                          <div className="text-center text-white z-10">
                            <IconComponent className="w-16 h-16 mx-auto mb-4 opacity-90" />
                            <h3 className="font-bold text-xl mb-2">{project.title}</h3>
                            <p className="text-sm opacity-90 mb-3">{project.category}</p>
                            <div className="flex items-center justify-center gap-2 text-xs opacity-80">
                              <Play className="w-3 h-3" />
                              <span>Click to view live demo</span>
                            </div>
                          </div>
                          {/* Decorative Pattern */}
                          <div className="absolute inset-0 opacity-10">
                            <div className="absolute top-4 right-4 w-12 h-12 border-2 border-white rounded-full"></div>
                            <div className="absolute bottom-4 left-4 w-8 h-8 border-2 border-white rounded-full"></div>
                            <div className="absolute top-1/2 left-4 w-6 h-6 border-2 border-white rounded-full"></div>
                            <div className="absolute top-8 left-1/2 w-4 h-4 border-2 border-white rounded-full"></div>
                          </div>
                        </div>
                      )}

                      {/* Image Overlay Gradient */}
                      <div
                        className={`
                        absolute inset-0 transition-opacity duration-500
                        ${isHovered ? "opacity-60" : "opacity-30"}
                      `}
                        style={{
                          background: `linear-gradient(to top, rgba(16, 19, 20, 0.9), rgba(16, 19, 20, 0.3), transparent)`,
                        }}
                      />

                      {/* Status and Year Badges */}
                      <div className="absolute top-4 right-4 flex gap-2">
                        <Badge
                          className={`
                            text-xs font-semibold backdrop-blur-sm
                            ${
                              project.status === "Live"
                                ? "bg-green-500/90 text-white border-green-400"
                                : "bg-yellow-500/90 text-black border-yellow-400"
                            }
                          `}
                        >
                          {project.status}
                        </Badge>
                      </div>

                      <div className="absolute top-4 left-4">
                        <Badge
                          className="backdrop-blur-sm border"
                          style={{
                            backgroundColor: "rgba(16, 19, 20, 0.8)",
                            color: "rgb(233, 100, 55)",
                            borderColor: "rgba(233, 100, 55, 0.5)",
                          }}
                        >
                          <Calendar className="w-3 h-3 mr-1" />
                          {project.year}
                        </Badge>
                      </div>

                      {/* Project Icon */}
                      <div
                        className={`
                        absolute bottom-4 left-4 transition-all duration-500
                        ${isHovered ? "scale-110 rotate-12" : "scale-100"}
                      `}
                      >
                        <div
                          className="w-14 h-14 backdrop-blur-sm rounded-xl flex items-center justify-center border-2 shadow-lg"
                          style={{
                            backgroundColor: "rgba(233, 100, 55, 0.9)",
                            borderColor: "rgb(233, 100, 55)",
                          }}
                        >
                          <IconComponent className="w-7 h-7 text-white" />
                        </div>
                      </div>

                      {/* Hover Overlay with Quick Actions */}
                      <div
                        className={`
                        absolute inset-0 backdrop-blur-sm flex items-center justify-center
                        transition-all duration-500
                        ${isHovered ? "opacity-100" : "opacity-0 pointer-events-none"}
                      `}
                        style={{ backgroundColor: "rgba(16, 19, 20, 0.85)" }}
                      >
                        <div className="text-center">
                          <Button
                            asChild
                            className="font-semibold px-8 py-4 rounded-xl transition-all duration-300 hover:scale-110 text-white mb-3 shadow-lg"
                            style={{ backgroundColor: "rgb(233, 100, 55)" }}
                          >
                            <a
                              href={project.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-3"
                            >
                              <Eye className="w-5 h-5" />
                              View Live Project
                            </a>
                          </Button>
                          <p className="text-xs text-gray-400">Click to explore the live demo</p>
                        </div>
                      </div>
                    </div>

                  {/* Content Section */}
                  <CardContent className="px-6 pt-4 pb-4 flex-1 flex flex-col">
                      {/* Title and Category */}
                      <div className="mb-4">
                        <div className="flex items-start justify-between mb-3">
                          <h3
                            className={`
                            text-xl font-bold transition-colors duration-300 leading-tight
                            ${isHovered ? "" : "text-white"}
                          `}
                            style={{ color: isHovered ? "rgb(233, 100, 55)" : "white" }}
                          >
                            {project.title}
                          </h3>
                          <Badge
                            variant="outline"
                            className="text-xs border ml-2"
                            style={{
                              borderColor: "rgba(233, 100, 55, 0.5)",
                              color: "rgb(233, 100, 55)",
                              backgroundColor: "rgba(233, 100, 55, 0.1)",
                            }}
                          >
                            {project.category}
                          </Badge>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-gray-300 text-sm leading-relaxed mb-4 flex-grow line-clamp-3">
                        {project.description}
                      </p>

                      {/* Key Features */}
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold text-gray-400 mb-2 uppercase tracking-wide">
                          Key Features
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {project.features.slice(0, 4).map((feature, featureIndex) => (
                            <Badge
                              key={featureIndex}
                              variant="secondary"
                              className="text-xs px-2 py-1"
                              style={{
                                backgroundColor: "rgba(233, 100, 55, 0.1)",
                                color: "rgba(233, 100, 55, 0.8)",
                                border: "1px solid rgba(233, 100, 55, 0.2)",
                              }}
                            >
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Technologies */}
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold text-gray-400 mb-2 uppercase tracking-wide">
                          Technologies
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {project.tools.slice(0, 3).map((tool, toolIndex) => (
                            <Badge
                              key={toolIndex}
                              variant="secondary"
                              className="text-xs text-gray-300 border-gray-700 hover:bg-gray-700 transition-colors"
                              style={{ backgroundColor: "rgba(16, 19, 20, 0.8)" }}
                            >
                              {tool}
                            </Badge>
                          ))}
                          {project.tools.length > 3 && (
                            <Badge
                              variant="secondary"
                              className="text-xs text-gray-400 border-gray-700"
                              style={{ backgroundColor: "rgba(16, 19, 20, 0.8)" }}
                            >
                              +{project.tools.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div
                        className={`
                        flex gap-2 transition-all duration-500
                        ${isHovered ? "opacity-100 translate-y-0" : "opacity-80 translate-y-1"}
                      `}
                      >
                        <Button
                          asChild
                          size="sm"
                          className="flex-1 font-medium transition-all duration-300 hover:scale-105 text-white shadow-md"
                          style={{ backgroundColor: "rgb(233, 100, 55)" }}
                        >
                          <a
                            href={project.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center justify-center gap-2"
                          >
                            <Eye className="w-3 h-3" />
                            Live Demo
                          </a>
                        </Button>

                        <Button
                          asChild
                          size="sm"
                          variant="outline"
                          className="flex-1 transition-all duration-300 hover:scale-105 bg-transparent text-white hover:text-white border"
                          style={{
                            borderColor: "rgba(233, 100, 55, 0.5)",
                            color: "rgb(233, 100, 55)",
                          }}
                        >
                          <a
                            href={project.github}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center justify-center gap-2"
                          >
                            <FaGithub className="w-3 h-3" />
                            Code
                          </a>
                        </Button>

                        <Button
                          asChild
                          size="sm"
                          variant="ghost"
                          className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-105"
                          style={{ backgroundColor: "rgba(16, 19, 20, 0.8)" }}
                        >
                          <a href={project.link} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-3 h-3" />
                          </a>
                        </Button>
                      </div>
                  </CardContent>
                </Card>
              </div>
            )
          })}
        </div>
      </div>

     
    </div>
  )
}
