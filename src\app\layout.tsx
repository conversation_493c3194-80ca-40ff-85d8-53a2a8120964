import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Moaz Raheem - Frontend Developer | React.js & Next.js Expert",
    template: "%s | Moaz Raheem - Frontend Developer"
  },
  description: "Professional portfolio of <PERSON><PERSON>hee<PERSON>, a skilled Frontend Developer specializing in React.js, Next.js, UI/UX design, and modern web technologies. Experienced in building responsive, high-performance web applications.",
  keywords: [
    "Moaz Raheem",
    "Frontend Developer",
    "React.js Developer",
    "Next.js Developer",
    "UI/UX Designer",
    "Web Developer",
    "JavaScript",
    "TypeScript",
    "Tailwind CSS",
    "Portfolio",
    "Responsive Design",
    "Web Applications",
    "Software Engineer",
    "Pakistan Developer"
  ],
  authors: [{ name: "<PERSON><PERSON>heem", url: "https://moazraheem.dev" }],
  creator: "Moaz Raheem",
  publisher: "Moaz Raheem",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://moazraheem.dev"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Moaz Raheem - Frontend Developer | React.js & Next.js Expert",
    description: "Professional portfolio of Moaz Raheem, a skilled Frontend Developer specializing in React.js, Next.js, and modern web technologies.",
    url: "https://moazraheem.dev",
    siteName: "Moaz Raheem Portfolio",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Moaz Raheem - Frontend Developer Portfolio",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Moaz Raheem - Frontend Developer | React.js & Next.js Expert",
    description: "Professional portfolio of Moaz Raheem, a skilled Frontend Developer specializing in React.js, Next.js, and modern web technologies.",
    images: ["/og-image.jpg"],
    creator: "@moazraheem",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/icon.svg", type: "image/svg+xml", sizes: "32x32" },
    ],
    apple: [
      { url: "/apple-touch-icon.svg", sizes: "180x180", type: "image/svg+xml" },
    ],
  },
  manifest: "/manifest.json",
  category: "technology",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" dir="ltr">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#e96437" />
        <meta name="color-scheme" content="dark light" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Moaz Portfolio" />
        <meta name="application-name" content="Moaz Portfolio" />
        <meta name="msapplication-TileColor" content="#e96437" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Person",
              "name": "Moaz Raheem",
              "jobTitle": "Frontend Developer",
              "description": "Professional Frontend Developer specializing in React.js, Next.js, and modern web technologies",
              "url": "https://moazraheem.dev",
              "sameAs": [
                "https://www.linkedin.com/in/moaz-raheem-react-js-next-js-developer-50a05528a",
                "https://github.com/mianmoaz786",
                "https://www.facebook.com/moaz.raheem.3",
                "https://www.instagram.com/mianmoaz225/"
              ],
              "knowsAbout": [
                "React.js",
                "Next.js",
                "JavaScript",
                "TypeScript",
                "UI/UX Design",
                "Frontend Development",
                "Web Development"
              ],
              "alumniOf": "GCUF",
              "nationality": "Pakistani"
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
