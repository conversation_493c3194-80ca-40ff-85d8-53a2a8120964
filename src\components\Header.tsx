'use client';

import { FaFacebook, FaInstagram, FaGithub, FaLinkedin, FaWhatsapp, FaTelegram } from 'react-icons/fa';
import { FaUser, FaGraduationCap, FaCogs, FaBriefcase, FaProjectDiagram, FaServicestack, FaEnvelope } from 'react-icons/fa';
import { HiCodeBracket } from "react-icons/hi2";
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Header = () => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuRef = useRef<HTMLDivElement>(null);
  const menuItemsRef = useRef<(HTMLButtonElement | null)[]>([]);

  const texts = [' NextJs Developer,ReactJs Developer', 'Web Developer','Ui Developer'];

  const menuItems = [
    { icon: FaUser, label: 'About', target: 'about' },
    { icon: FaGraduationCap, label: 'Education', target: 'education' },
    { icon: FaCogs, label: 'Skills', target: 'skills' },
    { icon: FaBriefcase, label: 'Experience', target: 'experience' },
    { icon: FaProjectDiagram, label: 'Projects', target: 'projects' },
    { icon: FaServicestack, label: 'Services', target: 'services' },
    { icon: FaEnvelope, label: 'Contact', target: 'contact' }
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);

    if (!isMenuOpen) {
      // Opening animation - smooth entry
      gsap.to(menuRef.current, {
        x: '0%',
        duration: 0.8,
        ease: 'power2.out'
      });

      // Animate menu items from left to right with same smooth effect as closing
      gsap.to(menuItemsRef.current, {
        x: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        delay: 0.5,
        ease: 'power2.out'
      });
    } else {
      // Closing animation - items slide back to left
      gsap.to(menuItemsRef.current, {
        x: -100,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: 'power2.in'
      });

      gsap.to(menuRef.current, {
        x: '-100%',
        duration: 0.8,
        delay: 0.8,
        ease: 'power2.in'
      });
    }
  };

  const downloadCV = () => {
    console.log('downloadCV function called!');
    alert('downloadCV function executed!'); // Temporary debug

    try {
      // Simple approach first
      const link = document.createElement('a');
      link.href = '/Moaz%20Raheem%20.pdf';
      link.download = 'Moaz_Raheem_CV.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      console.log('CV download completed');
    } catch (error) {
      console.error('Download error:', error);
      // Fallback: open in new tab
      window.open('/Moaz%20Raheem%20.pdf', '_blank');
    }
  };

  const scrollToContact = () => {
    console.log('scrollToContact function called!');
    alert('scrollToContact function executed!'); // Temporary debug

    try {
      const contactSection = document.querySelector('#contact');
      console.log('Contact section:', contactSection);

      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        console.log('Scrolled to contact section');
      } else {
        console.log('Contact section not found, scrolling to bottom');
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth'
        });
      }
    } catch (error) {
      console.error('Scroll error:', error);
    }
  };

  const navigateToSection = (target: string) => {
    const section = document.querySelector(`#${target}`);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
      // Close the menu after navigation
      setIsMenuOpen(false);

      // Animate menu items out
      gsap.to(menuItemsRef.current, {
        x: -100,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: 'power2.in'
      });

      gsap.to(menuRef.current, {
        x: '-100%',
        duration: 0.8,
        delay: 0.8,
        ease: 'power2.in'
      });
    }
  };

  // Initialize menu items position on mount to prevent jerk
  useEffect(() => {
    if (menuItemsRef.current.length > 0) {
      gsap.set(menuItemsRef.current, {
        x: -100,
        opacity: 0
      });
    }
  }, []);

  useEffect(() => {
    const currentText = texts[currentIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (displayText.length < currentText.length) {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        } else {
          // Pause before deleting
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        // Deleting
        if (displayText.length > 0) {
          setDisplayText(displayText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % texts.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [displayText, currentIndex, isDeleting, texts]);

  return (
    <header className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Side - Red Section */}
      <div style={{
        backgroundColor:'rgb(233, 100, 55)'
      }} className="w-full lg:w-1/2  flex flex-col justify-between p-4 md:p-6 lg:p-8 relative min-h-[50vh] lg:min-h-screen">
        {/* Top Section with Name */}
        <div className="flex items-center text-white">
          <HiCodeBracket className="text-3xl md:text-4xl lg:text-5xl mr-2" />
          <span className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">Moaz Raheem</span>
        </div>

        {/* Center Section with Profile Image */}
        <div className="flex justify-center items-center flex-1 lg:absolute lg:inset-0">
          <div className="relative">
            <div className="w-48 h-48 md:w-64 md:h-64 lg:w-80 lg:h-80 rounded-full border-4 md:border-6 lg:border-8 border-red-600 bg-gradient-to-br from-red-600 to-red-700 flex items-center justify-center">
              {/* Placeholder for profile image - replace with actual image */}
              <div className="w-44 h-44 md:w-60 md:h-60 lg:w-72 lg:h-72 rounded-full bg-gray-800 flex items-center justify-center text-white text-3xl md:text-4xl lg:text-6xl font-bold">
                MR
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section with Social Icons */}
        <div className="flex gap-2 md:gap-4 justify-center lg:justify-start z-10 mt-4 lg:mt-0">
          <a
            href="https://www.facebook.com/moaz.raheem.3"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaFacebook className="text-sm md:text-base" />
          </a>
          <a
            href="https://www.instagram.com/mianmoaz225/"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaInstagram className="text-sm md:text-base" />
          </a>
          <a
            href="https://github.com/mianmoaz786"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaGithub className="text-sm md:text-base" />
          </a>
          <a
            href="https://www.linkedin.com/in/moaz-raheem-react-js-next-js-developer-50a05528a"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaLinkedin className="text-sm md:text-base" />
          </a>
          <a
            href="https://wa.me/923019739945"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaWhatsapp className="text-sm md:text-base" />
          </a>
          <a
            href="#"
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaTelegram className="text-sm md:text-base" />
          </a>
        </div>
      </div>

      {/* Right Side - Dark Section */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-4 md:p-8 lg:p-16 text-white min-h-[50vh] lg:min-h-screen relative z-10" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
        {/* Greeting */}
        <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-2 md:mb-4">Hi There!</p>

        {/* Main Title */}
        <h1 className="text-3xl md:text-5xl lg:text-7xl font-bold mb-4 md:mb-6">
          <span className="text-white">I'M </span>
          <span className="text-[rgb(233,100,55)]" style={{ textShadow: 'none', filter: 'none' }}>Moaz Raheem</span>
        </h1>

        {/* Animated Subtitle */}
        <div className="text-lg md:text-2xl lg:text-3xl text-gray-300 mb-8 md:mb-12 h-8 md:h-10 lg:h-12">
          <span>{displayText}</span>
          <span className="animate-pulse text-[rgb(233,100,55)]">|</span>
        </div>
        
        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 md:gap-6 relative z-50">
          <button
            type="button"
            onMouseDown={() => console.log('Download CV mousedown')}
            onMouseUp={() => console.log('Download CV mouseup')}
            onClick={(e) => {
              console.log('Download CV onClick triggered!');
              alert('Download CV button works!');
              e.preventDefault();
              e.stopPropagation();
              downloadCV();
            }}
            className="px-4 md:px-6 lg:px-8 py-2 md:py-3 border-2 border-[rgb(233,100,55)] text-[rgb(233,100,55)] rounded-full hover:bg-[rgb(233,100,55)] hover:text-white transition-colors font-medium text-sm md:text-base cursor-pointer select-none"
            style={{
              pointerEvents: 'auto',
              position: 'relative',
              zIndex: 100
            }}
          >
            Download CV
          </button>
          <button
            type="button"
            onMouseDown={() => console.log('Contact mousedown')}
            onMouseUp={() => console.log('Contact mouseup')}
            onClick={(e) => {
              console.log('Contact onClick triggered!');
              alert('Contact button works!');
              e.preventDefault();
              e.stopPropagation();
              scrollToContact();
            }}
            className="px-4 md:px-6 lg:px-8 py-2 md:py-3 bg-[rgb(233,100,55)] text-white rounded-full border-2 border-[rgb(233,100,55)] hover:bg-transparent hover:text-[rgb(233,100,55)] transition-all duration-300 font-medium text-sm md:text-base cursor-pointer select-none"
            style={{
              pointerEvents: 'auto',
              position: 'relative',
              zIndex: 100
            }}
          >
            Contact
          </button>
        </div>
      </div>

      {/* Burger Menu Button */}
      <button
        onClick={toggleMenu}
        className="fixed top-4 right-4 md:top-6 md:right-6 z-50 w-10 h-10 md:w-12 md:h-12 rounded-md flex flex-col justify-center items-center gap-1 transition-all duration-300 hover:scale-110"
        style={{backgroundColor: 'rgb(16, 19, 20)'}}
      >
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-2' : ''}`}></span>
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`}></span>
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-2' : ''}`}></span>
      </button>

      {/* Side Navigation Menu */}
      <div
        ref={menuRef}
        className="fixed top-0 left-0 h-full w-72 md:w-80 z-40 flex flex-col justify-center items-start p-4 md:p-6 lg:p-8"
        style={{
          backgroundColor: 'rgb(16, 19, 20)',
          transform: 'translateX(-100%)'
        }}
      >
        <div className="flex flex-col gap-6 w-full">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <button
                key={item.label}
                ref={el => {
                  if (el) menuItemsRef.current[index] = el;
                }}
                onClick={() => navigateToSection(item.target)}
                className="flex items-center justify-center gap-3 md:gap-4 text-white text-base md:text-lg font-medium hover:text-white hover:bg-[rgb(233,100,55)] transition-all duration-300 p-3 md:p-4 w-full text-left rounded-[33px] border-2 border-white/30 hover:border-[rgb(233,100,55)] cursor-pointer"
              >
                <IconComponent size={20} />
                <span>{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30"
          onClick={toggleMenu}
        />
      )}
    </header>
  );
};

export default Header;
